#include "raylib.h"
#include "Entity.h"  // Include our Entity class

int main() {
    // Window configuration
    const int screenWidth = 1000;
    const int screenHeight = 600;

    // Initialize the window
    InitWindow(screenWidth, screenHeight, "My First Raylib C++ Application!!!");

    // Set target FPS (frames per second)
    SetTargetFPS(30);  // Increased to 60 FPS for smoother movement

    // Create our player entity
    // Parameters: x, y, size, color, speed
    Entity player(screenWidth/2 - 25, screenHeight/2 - 25, 50, BLUE, 20.0f);

    // Main game loop
    while (!WindowShouldClose()) {    // Detect window close button or ESC key
        // Update
        // Handle keyboard input for player movement
        // Check for keyboard input and move the player
        if (IsKeyDown(KEY_W) || IsKeyDown(KEY_UP)) {
            player.MoveUp();
        }
        if (IsKeyDown(KEY_S) || IsKeyDown(KEY_DOWN)) {
            player.MoveDown();
        }
        if (IsKeyDown(KEY_A) || IsKeyDown(KEY_LEFT)) {
            player.MoveLeft();
        }
        if (IsKeyDown(KEY_D) || IsKeyDown(KEY_RIGHT)) {
            player.MoveRight();
        }

        // Keep the player within screen boundaries
        player.ClampToBounds(screenWidth, screenHeight);

        // Update the player entity (currently empty, but good practice)
        player.Update();

        // Get mouse position for display
        Vector2 mousePos = GetMousePosition();

        // Draw
        BeginDrawing();

            // Clear background with a nice color
            ClearBackground(RAYWHITE);

            // Draw our player entity
            player.Draw();

            // Draw instructions for the player
            DrawText("Use WASD or Arrow Keys to move the blue square!", 10, 10, 20, DARKGRAY);
            DrawText("The square will stay within the screen boundaries.", 10, 35, 16, GRAY);

            // Display mouse coordinates in top-left corner
            DrawText(TextFormat("Mouse: X=%.0f, Y=%.0f", mousePos.x, mousePos.y), 10, 60, 18, DARKBLUE);

            // Alternative: Display mouse coordinates following the cursor
            // (Uncomment the line below and comment the line above to use this instead)
            // DrawText(TextFormat("X=%.0f, Y=%.0f", mousePos.x, mousePos.y), mousePos.x + 15, mousePos.y - 20, 16, RED);

            // Draw FPS counter
            DrawFPS(10, screenHeight - 25);

        EndDrawing();
    }

    // Close window and OpenGL context
    CloseWindow();

    return 0;
}
