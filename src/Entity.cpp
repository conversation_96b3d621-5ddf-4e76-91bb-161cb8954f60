#include "Entity.h"

/**
 * Constructor - This is called when you create a new Entity object
 * Example: Entity player(100, 100, 50, RED, 5.0f);
 * 
 * The initialization list (after the colon) is a C++ feature that
 * directly initializes member variables - it's more efficient than
 * assigning values inside the constructor body.
 */
Entity::Entity(float startX, float startY, float size, Color entityColor, float moveSpeed)
    : x(startX), y(startY), width(size), height(size), color(entityColor), speed(moveSpeed)
{
    // Constructor body - we could do additional setup here if needed
    // For this simple entity, the initialization list handles everything
}

/**
 * Destructor - This is called when the Entity object is destroyed
 * For our simple entity, we don't need to clean up anything special
 * (no dynamic memory allocation, file handles, etc.)
 */
Entity::~Entity()
{
    // Nothing to clean up for this simple entity
}

/**
 * Update method - Called every frame to update the entity's state
 * This is where you would put game logic like:
 * - Animation updates
 * - Physics calculations
 * - AI behavior
 * 
 * For now, it's empty, but we'll use it later if needed
 */
void Entity::Update()
{
    // Currently empty - movement is handled by the specific movement methods
    // You could add things like:
    // - Automatic movement patterns
    // - Animation frame updates
    // - Physics updates (gravity, velocity, etc.)
}

/**
 * Draw method - Renders the entity on screen using Raylib functions
 * This should be called between BeginDrawing() and EndDrawing()
 */
void Entity::Draw()
{
    // DrawRectangle takes: x, y, width, height, color
    // We cast float to int because DrawRectangle expects integers
    DrawRectangle((int)x, (int)y, (int)width, (int)height, color);
    
    // Optional: Draw a border around the entity for better visibility
    // DrawRectangleLines((int)x, (int)y, (int)width, (int)height, BLACK);
}

/**
 * Movement methods - These move the entity in different directions
 * Each method updates the position by the entity's speed value
 */
void Entity::MoveUp()
{
    y -= speed;  // Subtract from y because (0,0) is top-left in screen coordinates
}

void Entity::MoveDown()
{
    y += speed;  // Add to y to move down the screen
}

void Entity::MoveLeft()
{
    x -= speed;  // Subtract from x to move left
}

void Entity::MoveRight()
{
    x += speed;  // Add to x to move right
}

/**
 * Getter methods - These allow other code to read the entity's properties
 * The 'const' keyword means these methods promise not to modify the object
 * This is good practice and allows the methods to be called on const objects
 */
float Entity::GetX() const
{
    return x;
}

float Entity::GetY() const
{
    return y;
}

float Entity::GetWidth() const
{
    return width;
}

float Entity::GetHeight() const
{
    return height;
}

Color Entity::GetColor() const
{
    return color;
}

/**
 * Setter methods - These allow other code to modify the entity's properties
 * It's good practice to provide controlled access to private data
 */
void Entity::SetPosition(float newX, float newY)
{
    x = newX;
    y = newY;
}

void Entity::SetColor(Color newColor)
{
    color = newColor;
}

void Entity::SetSpeed(float newSpeed)
{
    // You could add validation here, e.g., ensure speed is positive
    if (newSpeed >= 0.0f) {
        speed = newSpeed;
    }
}

/**
 * Utility method to check if the entity is completely within screen boundaries
 * Returns true if the entity is fully inside the screen, false otherwise
 */
bool Entity::IsWithinBounds(int screenWidth, int screenHeight)
{
    return (x >= 0 && y >= 0 && 
            x + width <= screenWidth && 
            y + height <= screenHeight);
}

/**
 * Method to keep the entity within screen boundaries
 * This "clamps" the position so the entity can't go off-screen
 */
void Entity::ClampToBounds(int screenWidth, int screenHeight)
{
    // Clamp X position
    if (x < 0) {
        x = 0;
    } else if (x + width > screenWidth) {
        x = screenWidth - width;
    }
    
    // Clamp Y position
    if (y < 0) {
        y = 0;
    } else if (y + height > screenHeight) {
        y = screenHeight - height;
    }
}
